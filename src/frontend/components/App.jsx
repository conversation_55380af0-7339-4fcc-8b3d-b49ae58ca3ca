import React, { useState, useRef, useEffect } from 'react';
import ConversationPane from './ConversationPane.jsx';
import SummaryPane from './SummaryPane.jsx';

/**
 * App - 主应用容器
 *
 * - 将 ConversationPane 与 SummaryPane 组合为左右双栏（桌面）/单栏（移动）布局
 * - 管理 messages 与 summaries 的共享状态
 * - 实现 Summary 点击跳转到对应 message 的逻辑（通过 ConversationPane 暴露的 ref）
 *
 * 参考文件：
 * - [`src/frontend/components/ConversationPane.jsx`](src/frontend/components/ConversationPane.jsx:1)
 * - [`src/frontend/components/SummaryPane.jsx`](src/frontend/components/SummaryPane.jsx:1)
 */
export default function App() {
  // 示例初始数据（可以替换为实际 API 拉取）
  const initialMessages = [
    { message_id: 'm1', sender: 'user', content: '你好，我需要一些帮助。' },
    { message_id: 'm2', sender: 'assistant', content: '当然，请告诉我具体问题。' },
    { message_id: 'm3', sender: 'user', content: '如何集成摘要点击跳转？' },
    { message_id: 'm4', sender: 'assistant', content: '你可以在父组件中持有 ConversationPane 的 scrollTo 引用。' },
  ];

  const initialSummaries = [
    { summary_id: 's1', title: '会话概览', text: '用户: 你好\n助手: 当然，请告诉我具体问题。', related_message_id: 'm2' },
    { summary_id: 's2', title: '跳转示例', text: '{"user":"如何集成摘要点击跳转？","assistant":"你可以在父组件中持有 ConversationPane 的 scrollTo 引用。"}', related_message_id: 'm3' }
  ];

  const [messages, setMessages] = useState(initialMessages);
  const [summaries, setSummaries] = useState(initialSummaries);

  // ref 用于接收 ConversationPane 暴露的 scrollToMessage 函数
  // ConversationPane 支持 ref 对象：onScrollToMessage.current = scrollToMessage
  const scrollRef = useRef(null);

  // 将新消息添加到 messages（示例 onSend 回调）
  function handleSend(text) {
    const newMessage = {
      message_id: `m${Date.now()}`,
      sender: 'user',
      content: text
    };
    setMessages(prev => [...prev, newMessage]);
  }

  // 当 Summary 被选中时：尝试使用 related_message_id 或从 summary 内容推断目标 message_id
  function handleSummarySelect(summary) {
    let targetMessageId = summary.related_message_id || summary.message_id || summary.target_message_id;

    // 如果 summary 中没有直接字段，尝试从文本里查找已知 message 内容（简单匹配）
    if (!targetMessageId) {
      for (const m of messages) {
        if (summary.text && summary.text.includes(m.content)) {
          targetMessageId = m.message_id || m.id;
          break;
        }
      }
    }

    if (targetMessageId && scrollRef && scrollRef.current) {
      // scrollRef.current 应为 ConversationPane 暴露的 scrollTo(messageId) 函数
      try {
        scrollRef.current(targetMessageId, { highlight: true, behavior: 'smooth' });
      } catch (e) {
        // 兼容性回退：如果 scrollRef.current 为一个对象（ref 风格）
        if (typeof scrollRef.current === 'object' && scrollRef.current.current) {
          scrollRef.current.current(targetMessageId, { highlight: true, behavior: 'smooth' });
        }
      }
    }
  }

  // 简单响应式样式：左右两列在宽屏显示，窄屏垂直堆叠
  const containerStyle = {
    display: 'flex',
    flexDirection: 'row',
    height: '100vh',
    width: '100%',
  };

  const leftStyle = {
    flex: 1,
    minWidth: 0,
    display: 'flex',
    flexDirection: 'column'
  };

  const rightStyle = {
    width: 360,
    borderLeft: '1px solid #eee',
    overflow: 'auto',
    backgroundColor: '#fafafa'
  };

  // media query fallback: if window is narrow, stack columns
  const [isNarrow, setIsNarrow] = useState(false);
  useEffect(() => {
    function onResize() {
      setIsNarrow(window.innerWidth < 800);
    }
    onResize();
    window.addEventListener('resize', onResize);
    return () => window.removeEventListener('resize', onResize);
  }, []);

  return (
    <div style={{ height: '100vh', width: '100%' }}>
      <div style={isNarrow ? { ...containerStyle, flexDirection: 'column' } : containerStyle}>
        <div style={leftStyle}>
          <ConversationPane
            messages={messages}
            onSend={handleSend}
            onScrollToMessage={scrollRef}
            enableLazyLoading={false}
            autoScrollToBottom={true}
          />
        </div>

        <div style={isNarrow ? { ...rightStyle, width: '100%', borderLeft: 'none', borderTop: '1px solid #eee' } : rightStyle}>
          <SummaryPane
            summaries={summaries}
            onSelect={handleSummarySelect}
          />
        </div>
      </div>
    </div>
  );
}
